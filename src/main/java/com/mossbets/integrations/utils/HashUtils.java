package com.mossbets.integrations.utils;

import com.google.gson.JsonObject;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.google.gson.Gson;
import static com.mossbets.integrations.utils.Utilities.toSHA1;
import jakarta.enterprise.context.ApplicationScoped;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * HashUtils
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class HashUtils {

    private static final Gson gson = new Gson();



    /**
     * CreateHeaderXappKey
     *
     * @param key
     * @return
     * @throws NoSuchAlgorithmException md5(sha1($apiKey . ''
     * .$request['timestamp']))
     */
    public static String CreateHeaderXappKey(String key) throws NoSuchAlgorithmException {
        return Utilities.CreateMd5(toSHA1(key));
    }

    /**
     * HashCalculate
     *
     * @param request
     * @param hashKey
     * @param cmpKey
     * @param isTest
     * @return
     */
    public static boolean HashCalculate(String request, String hashKey, String cmpKey, boolean isTest) {
        boolean result = false;
        try {
            if (isTest) {
                return true;
            }

            if (Utilities.JsonValidator(request, false)) {
                String resHash = hashCreate(request, cmpKey);
                if (hashKey.equalsIgnoreCase(resHash)) {
                    result = true;
                }
            }
        } catch (NoSuchAlgorithmException ex) {
        }

        return result;
    }

    /**
     * hashCreate
     *
     * @param requestPayload
     * @param tokenKey
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String hashCreate(String requestPayload, String tokenKey)
            throws NoSuchAlgorithmException {

        JsonObject request = JsonParser.parseString(requestPayload).getAsJsonObject();

        Set<Map.Entry<String, JsonElement>> entries = request.entrySet();
        List<String> sortedKeys = new ArrayList<>();
        entries.forEach(entry -> {
            sortedKeys.add(entry.getKey());
        });
        
        Collections.sort(sortedKeys);
        StringBuilder hashkey = new StringBuilder();

        sortedKeys.forEach(k -> {
            JsonElement v = request.get(k);
            if (v.isJsonObject()) {
                JsonObject valueObj = v.getAsJsonObject();
                Set<Map.Entry<String, JsonElement>> keysObj = valueObj.entrySet();

                List<String> sortedKeysObj = new ArrayList<>();
                keysObj.forEach(entry -> {
                    sortedKeysObj.add(entry.getKey());
                });

                Collections.sort(sortedKeysObj);
                StringBuilder hashVal = new StringBuilder();
                sortedKeysObj.forEach((String valueKey) -> {
                    JsonElement val = valueObj.get(valueKey);
                    if (val.isJsonObject()) {
                        val = sortJsonObject(val.getAsJsonObject());
                    } else if (val.isJsonArray()) {
                        val = sortJsonArray(val.getAsJsonArray());
                    }

                    try {
                        hashVal.append("&")
                                .append(valueKey)
                                .append("=")
                                .append(Utilities.CreateMd5(gson.toJson(val)));
                    } catch (NoSuchAlgorithmException ex) {
                        Logger.getLogger(HashUtils.class.getName())
                                .log(Level.SEVERE, null, ex);
                    }
                });

                hashkey.append(hashVal);
            } else if (v.isJsonArray()) {
                JsonArray valueArr = v.getAsJsonArray();
                StringBuilder hashVal = new StringBuilder();

                for (int i = 0; i < valueArr.size(); i++) {
                    JsonElement val = valueArr.get(i);
                    try {
                        hashVal.append("&")
                                .append(i).append("=")
                                .append(Utilities.CreateMd5(gson.toJson(val)));
                    } catch (NoSuchAlgorithmException ex) {
                        Logger.getLogger(HashUtils.class.getName())
                                .log(Level.SEVERE, null, ex);
                    }
                }

                hashkey.append(hashVal);
            } else {
                hashkey
                        .append("&")
                        .append(k).append("=")
                        .append(v.getAsString());
            }
        });

        return Utilities
                .CreateMd5(hashkey
                        .toString()
                        .substring(1)
                        .concat(tokenKey));
    }

    /**
     * sortJsonObject
     *
     * @param obj
     * @return
     */
    private static JsonObject sortJsonObject(JsonObject obj) {
        JsonObject sortedObj = new JsonObject();
        List<String> sortedKeys = new ArrayList<>(obj.keySet());
        Collections.sort(sortedKeys);
        sortedKeys.forEach(key -> {
            JsonElement value = obj.get(key);
            if (value.isJsonObject()) {
                sortedObj.add(key, sortJsonObject(value.getAsJsonObject()));
            } else if (value.isJsonArray()) {
                sortedObj.add(key, sortJsonArray(value.getAsJsonArray()));
            } else {
                sortedObj.add(key, value);
            }
        });
        return sortedObj;
    }

    /**
     * sortJsonArray
     *
     * @param arr
     * @return
     */
    private static JsonArray sortJsonArray(JsonArray arr) {
        JsonArray sortedArr = new JsonArray();
        for (JsonElement value : arr) {
            if (value.isJsonObject()) {
                sortedArr.add(sortJsonObject(value.getAsJsonObject()));
            } else if (value.isJsonArray()) {
                sortedArr.add(sortJsonArray(value.getAsJsonArray()));
            } else {
                sortedArr.add(value);
            }
        }
        return sortedArr;
    }

}
