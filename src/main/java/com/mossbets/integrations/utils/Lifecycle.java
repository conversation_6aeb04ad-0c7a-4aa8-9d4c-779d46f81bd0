package com.mossbets.integrations.utils;

import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import org.jboss.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class Lifecycle {

    private final Logger logger = Logger.getLogger(Lifecycle.class);

    /**
     * onStart
     *
     * @param event
     */
    void onStart(@Observes StartupEvent event) {
        String license = """
                         [+]**********************************************[+]
                         [+]**********************************************[+]
                         [+]**********************************************[+]
                         [+]********          Mply-App           *********[+]
                         [+]********                             *********[+]
                         [+]********         Lidenlabs.com        *********[+]
                         [+]********  AUTHOR: JOSEPHAT MUKUHA    *********[+]
                         [+]**********************************************[+]
                         [+]**********************************************[+]
                         """;

        logger.info(license);
        logger.info(Utilities.GetUTCDate("")
                + " - ********** - [The application is starting...]");
    }

    /**
     * onStop
     *
     * @param event
     */
    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.GetUTCDate("") + " - ********** - [Application shutting down...]");
    }

}
