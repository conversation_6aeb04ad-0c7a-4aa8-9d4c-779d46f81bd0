package com.mossbets.integrations.utils.props;

import com.mossbets.integrations.controllers.resources.DB;
import com.mossbets.integrations.utils.JedisUtils;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.*;

@ApplicationScoped
public class LeaderboardService {

    @Inject
    JedisUtils jedisUtils;

    @Inject
    public DB dbService;

    @Inject
    public Logger logger;

    private static final Logger LOG = Logger.getLogger(LeaderboardService.class);
    @WithSpan
    public Optional<Map<String, Object>> getLeaderboardCampaign(Integer campaignPromoId) {
        String key = "LeaderboardService:GetLeaderboardCampaign";
        if (campaignPromoId != null) {
            key += ":PromoId:" + campaignPromoId;
        }

        String redisData = jedisUtils.fetchOneData(key);
        Optional<Map<String, Object>> dataObject = parseStringToMap(redisData);
        if (!isOptionalEmpty(String.valueOf(dataObject))) {
            return dataObject;
        } else {
            String sql = "SELECT ps.id, ps.min_stake, p.promo_name, p.promo_url, ps.min_odds, " +
                    "p.promo_details, p.promo_images FROM promotion_settings ps " +
                    "JOIN promotion p ON ps.promo_id = p.id " +
                    "JOIN promotion_type pt ON p.promo_type_id = pt.id " +
                    "WHERE pt.component = 'LEADERBOARD' AND pt.status = 1 AND p.status = 1 " +
                    "AND ps.status = 1 AND NOW() >= p.starting_date AND ending_date > NOW() " +
                    "ORDER BY ps.id ASC LIMIT 1";
            try (Connection conn = dbService.getReadDB("dbBonus").getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        Map<String, Object> result = new HashMap<>();
                        for (int i = 1; i <= rs.getMetaData().getColumnCount(); i++) {
                            result.put(rs.getMetaData().getColumnLabel(i), rs.getObject(i));
                        }
                        Optional<Map<String, Object>> finalResult = Optional.ofNullable(result);
                        String finalKey = key;
                        finalResult.ifPresent(data -> jedisUtils.saveData(finalKey, data.toString(), 3600));
                        return Optional.of(result);
                    }
                    return Optional.empty();
                }
            } catch (SQLException e) {
                logger.errorf("Failure to extract data" + sql);
                throw new RuntimeException("Query failed on ");
            }
        }
    }

    @WithSpan
    public boolean createLeaderboardEntry(String profileId, double betAmount) {
        try {
            Optional<Map<String, Object>> promoOpt = getLeaderboardCampaign(null);
            logger.info("Picked ongoing promotion : " + promoOpt);
            if (promoOpt.isEmpty()) {
                return false;
            } else {
                Map<String, Object> promo = promoOpt.get();
                double minStake = ((Number) promo.get("min_stake")).doubleValue();
                if (betAmount < minStake) {return false;}
                else{Map<String, Object> params = new HashMap<>();
                    params.put("promo_id", promo.get("id"));
                    params.put("profile_id", profileId);

                    String query = "SELECT id, status FROM transaction_leaderboard_stats WHERE promo_id = ?" +
                            " AND profile_id = ? AND promo_date = CURRENT_DATE";

                    try (Connection conn = dbService.getReadDB("trxn").getConnection();
                         PreparedStatement stmt = conn.prepareStatement(query)) {
                        stmt.setInt(1, (Integer) promo.get("id"));
                        stmt.setString(2, profileId);
                        Map<String, Object> result = new HashMap<>();
                        try (ResultSet rs = stmt.executeQuery()) {
                            if (isResultSetEmpty(rs)) {
                                    Map<String, Object> newEntry = new HashMap<>();
                                    newEntry.put("promo_id", promo.get("id"));
                                    newEntry.put("profile_id", profileId);
                                    newEntry.put("bet_count", 1);
                                    newEntry.put("stake", betAmount);
                                    newEntry.put("status", 1);
                                    newEntry.put("promo_date", LocalDate.now().toString());
                                    newEntry.put("created_at", LocalDate.now().toString());

                                    logger.info("Creating leader board entry with amount :" + betAmount);
                                    long insertedId = dbService.insert("trxn", "transaction_leaderboard_stats", newEntry);
                                    Map<String, Object> response = new HashMap<>();
                                    response.put("id", insertedId);
                                    response.put("type", "new");
                                    response.put("name", promo.get("promo_name"));
                                    response.put("url", promo.get("promo_url"));
                                    return true;
                            } else {
                                if (rs.next()) {
                                    result.put("id", rs.getString("id"));
                                    result.put("status", rs.getString("status"));
                                    Optional<Map<String, Object>> resultCheck = Optional.of(result);
                                    params.put("id", result.get("id"));
                                    params.put("stake", String.format("%.2f", betAmount)); // Format to two decimals
                                    logger.info("Updating leader board entry with with new stake : " + betAmount);
                                    String update = "UPDATE transaction_leaderboard_stats SET bet_count = bet_count + 1, " +
                                            "stake = stake + ? WHERE id = ? AND status = 1 LIMIT 1";
                                    try (Connection conn2 = dbService.getReadDB("trxn").getConnection();
                                         PreparedStatement newStmt = conn.prepareStatement(update)) {
                                        newStmt.setString(1, String.valueOf(betAmount));
                                        newStmt.setString(2, result.get("id").toString());

                                        int rowsUpdated = newStmt.executeUpdate();
                                        if (rowsUpdated == 0) {
                                            logger.error("Failed to update promotion");
                                            return false;
                                        } else {
                                            Map<String, Object> response = new HashMap<>();
                                            response.put("id", result.get("id"));
                                            response.put("type", "exists");
                                            response.put("name", promo.get("promo_name"));
                                            response.put("url", promo.get("promo_url"));
                                            return true;
                                        }
                                    } catch (SQLException e) {
                                        logger.error("Error updating promo for player : " + profileId, e);
                                        return false;
                                    }


                                }
                            }
                        } catch (SQLException e) {
                            logger.error("Error fetching promotion for profile: " + profileId, e);
                            return false;
                        }

                    } catch (SQLException e) {
                        throw new RuntimeException(e);}

                }
            }
        } catch (Exception e) {
            LOG.error("Error creating leaderboard entry", e);
            return false;
        }
        return false;
    }
    @WithSpan
    public boolean updateOdds(String profileId, double odds, double betAmount ) {
        Optional<Map<String, Object>> promoOpt = getLeaderboardCampaign(null);
        if (promoOpt.isEmpty()) {
            return false;
        }
        else{
            Map<String, Object> promo = promoOpt.get();
            double minStake = ((Number) promo.get("min_stake")).doubleValue();
            double minOdds = ((Number) promo.get("min_odds")).doubleValue();
            if (betAmount < minStake){ return false;}

            Map<String, Object> params = new HashMap<>();
            params.put("promo_id", promo.get("id"));
            params.put("profile_id", profileId);
            String query = "SELECT id, status, odds FROM transaction_leaderboard_stats WHERE promo_id = ?" +
                    "AND profile_id = ? AND promo_date = CURRENT_DATE";

            try (Connection conn = dbService.getReadDB("trxn").getConnection();
                 PreparedStatement stmt = conn.prepareStatement(query)) {
                stmt.setString(1, promo.get("id").toString());
                stmt.setString(2, profileId);

                Map<String, Object> result = new HashMap<>();
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        result.put("id", rs.getString("id"));
                        result.put("status", rs.getString("status"));
                        result.put("odds",rs.getString("odds"));
                        double dbOdds = Double.parseDouble(result.get("odds").toString());
                        if (odds < minOdds) {
                            return false;
                        } else{
                            if(dbOdds>=odds){
                                return false;
                            } else{
                                String update = "UPDATE transaction_leaderboard_stats SET odds = ? " +
                                        "WHERE id = ? AND status = 1 LIMIT 1";
                                logger.info("Updating leader board entry with odds : " + odds);
                                try (Connection conn2 = dbService.getReadDB("trxn").getConnection();
                                     PreparedStatement newStmt = conn.prepareStatement(update)) {
                                    newStmt.setDouble(1, odds);
                                    newStmt.setString(2, result.get("id").toString());
                                    int rowsUpdated = newStmt.executeUpdate();
                                    if (rowsUpdated == 0) {
                                        return false;
                                    } else {
                                        return true;
                                    }
                                } catch (SQLException e) {
                                    return false;
                                }
                            }

                        }
                    }
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
        return false;
    }
    public static double roundDoubleForDatabase(double value) {
        try {
            BigDecimal bd = BigDecimal.valueOf(value);
            bd = bd.setScale(2, RoundingMode.HALF_UP);
            return bd.doubleValue();
        } catch (Exception e) {
            return 0.0;
        }
    }
    public static Optional<Map<String, Object>> parseStringToMap(String input) {
        if (input == null || input.trim().isEmpty() || input.trim().equals("{}")) {
            return Optional.empty();
        }

        Map<String, Object> map = new HashMap<>();

        // Remove enclosing braces
        String content = input.trim();
        if (content.startsWith("{") && content.endsWith("}")) {
            content = content.substring(1, content.length() - 1);
        }

        // Split on commas not inside URLs or unescaped values
        String[] pairs = content.split(", (?=[^=]+=)");

        for (String pair : pairs) {
            String[] kv = pair.split("=", 2);
            if (kv.length == 2) {
                String key = kv[0].trim();
                String value = kv[1].trim();

                Object parsedValue;

                if ("null".equalsIgnoreCase(value)) {
                    parsedValue = null;
                } else if (value.matches("\\d+")) {
                    parsedValue = Integer.parseInt(value);
                } else if (value.matches("\\d+\\.\\d+")) {
                    parsedValue = Double.parseDouble(value);
                } else {
                    parsedValue = value;
                }

                map.put(key, parsedValue);
            }
        }

        return Optional.of(map);
    }
    public static boolean isOptionalEmpty(String input) {
        if (input == null || !input.startsWith("Optional[") || !input.endsWith("]")) {
            return true; // Treat invalid format as empty
        }

        // Extract inner content
        String inner = input.substring("Optional[".length(), input.length() - 1).trim();

        // Check if it's empty or just empty map notation
        return inner.isEmpty() || "{}".equals(inner);
    }
    public boolean isResultSetEmpty(ResultSet resultSet) throws SQLException {
        return !resultSet.isBeforeFirst();
    }
}