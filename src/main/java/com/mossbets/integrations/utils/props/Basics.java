package com.mossbets.integrations.utils.props;

import jakarta.enterprise.context.ApplicationScoped;
import java.util.Set;

import jakarta.inject.Inject;
import jakarta.annotation.PostConstruct;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Data
@ApplicationScoped
public class Basics {

    @Inject
    Props props;

    public long RequestExp = 240;//Seconds
    public long TokenExpiryPeriod = 168;//hours

    public String ServerName;
    public String launchUrl;
    public String CipherUrl;
    public String EncryptionKey;
    public String UA = "MPLY_APP_V2;Quarkus-3.12.3";

    @PostConstruct
    void init() {
        ServerName = props.serverName();
        launchUrl = props.launchUrl();
        CipherUrl = props.cipherUrl();
        EncryptionKey = props.encryptionKey();
    }

    //Keys
    public String playerBalancekey = "BALANCE:CID:{clientId}:PID:{PlayerId}";
    public String fbSettingsKey = "InitGame:FreebetCheck:GameCode:{gameCode}:CID:{clientId}";
    public String gameSettingKey = "GameCode:MossPlay:Authorization${gameCode}${clientId}";
    public String gameSettingAllKey = "GameCode:MossPlay:Authorization$";
    public String playerKey = "MossPlay:Authorization${PlayerId}";
    public String playerFreebetKey = "Freebet:GameCode:{gameCode}:CID:{clientId}:PID:{PlayerId}";
    public String clientAPIKey = "WalletUtils$GetClientApplicationKeys:CLIENT_KEYS;V2;{ClientId}";
    public String clientNewAPIKey = "WalletUtils$GetClientApplicationKeys:CLIENT_KEYS;V2;{Checkmd5}";
    public String TurboOnlinePlayers = "MossPlay:Authorization:PlayersOnline:ClientId:{gameCode}:GameCode:{clientId}";
    public String FreebetRainsPerPlayer = "MossPlay:PlayerRains:GameCode#{gameCode}:CID#{clientId}:PlayerId#{playerId}";
    public String FreebetRainsPerGame = "Fetch_Rains:GameCode#{gameCode}:ClientId#{clientId}";
    public String gameBoxJPSettingKey = "GameCode:MossPlay:Jacopots${gameCode}${clientId}";
    public String FreebetsPerGameSettingKey = "MossPlay:GameCode:MossPlay:FetchFreebet${gameCode}${clientId}";

    public int ConnectTimeOut = 60000;
    public int SocketTimeOut = 60000;

    public double demoBalance = 10000.00;

    public int[] GameIds = {1, 2, 6, 7};
    public int[] LocalScheduledVLCompetionIds = {9, 10, 11, 12};//CompetionIds
    public int[] TournamentScheduledVLRoundNo = {2, 3, 4};//CompetionIds
    public int[] succeessHttpStatuses = new int[]{200, 201, 202};
    public int[] failedHttpCodes = {0, 500, 502, 503, 504};//403,405
    public int[] JPStatuses = {1, 3, 5};


    public String SenderId = "MOSSBETS_TS";
    
    public final Set<Integer> SUCCESS_CODES = Set.of(200, 201, 202);

    //Freebets & Rains
    public final int OperatorIssuedFreebetReferenceTypeId = 47;
    public int[] AllowedFreebetGameIds = {1, 2, 6, 7};
    public int[] AllowedFreebetRainsGameIds = {6, 8};

    //Ingame Jackpots
    public String[] InGameJackpotTiers = new String[]{"Mini", "Major", "Mega"};

    //JP Status
    public int JPBetPending = 5;
    public int JPBetVoided = 3;
    public int JPBetResulted = 1;
    public String logoUrlFormat = "https://storage.googleapis.com/mbg-assets/team_logo/team_"
            + "{competition_id}_{short_name}.png";

    //Posting Bets
    public int PostedBetPending = 1;
    public int PostedBetSuccess = 2;
    public int PostedBetFailed = 5;

    //Operator API BetStatus
    public int ApiWonBetStatus = 0;
    public int ApiLostBetStatus = 3;
    public int ApiPendingBetStatus = 1;
    public int ApiVoidedBetStatus = 7;
    public int ApiCancelledBetStatus = 9;

    //System API BetStatus
    public int SysWonBetStatus = 1;
    public int SysLostBetStatus = 3;
    public int SysPlaceBetStatus = 4;
    public int SysPendingBetStatus = 5;
    public int SysVoidedBetStatus = 7;
    public int SysCancelledBetStatus = 9;

    //System API BetSlipStatus
    public int SysWonBetSlipStatus = 1;
    public int SysLostBetSlipStatus = 3;
    public int SysPlaceBetSlipStatus = 4;
    public int SysPendingBetSlipStatus = 5;
    public int SysVoidedBetSlipStatus = 7;
    public int SysCancelledBetSlipStatus = 9;

    //Turbo API Processing Status
    public final int TurboPendingProcessing = 189;
    public final int TurboOngoingProcessing = 200;
    public final int TurboFailedProcessing = 300;
    public final int TurboFinalProcessing = 208;

    //Turbo API BetStatus
    public final int TurboWonBetStatus = 0;
    public final int TurboLostBetStatus = 3;
    public final int TurboPendingBetStatus = 1;
    public final int TurboVoidedBetStatus = 7;
    public final int TurboCancelledBetStatus = 9;

    //Turbo Winning referenceId
    public final int KIRON_TURBO_BET_WINNINGS = 37;

    //Transaction ReferenceIds
    public final int TURBO_PLACEBET_REFUND_ID = 28;
    public final int TURBO_PLACEBET_REFERENCE_TYPE_ID = 36;
    public final int KALAKALA_PLACEBET_REFERENCE_TYPE_ID = 45;

    //TransactionReferences
    public int TRANSACTION_TYPE_CREDIT_ID = 1;
    public int TRANSACTION_TYPE_DEBIT_ID = 2;
    public int TRANSACTION_TYPE_REVERSAL_ID = 3;
    public int TRANSACTION_TYPE_VOIDED = 4;
    public int TRANSACTION_TYPE_TAXES = 5;
    public int TRANSACTION_TYPE_BONUS_BOOST = 6;
    public int TRANSACTION_TYPE_FREEBET_BOOST = 7;
    public int TRANSACTION_TYPE_FREEBET_OPERATOR_ISSUED = 8;

    //Query Params
    public int SelectionLimit = 20;
    public int[] exportParams = {0, 1};
    public int[] SkipCacheParams = {1, 2};
    public int[] GameStatusParams = {0, 1, 4};

    //Rand Vars
    public final int minRand = 0;
    public final int maxRand = 1;

    //queue
    public final String RefundQueueName = "BET_REFUNDS";
    public final String GameResultingQueueName = "RESULTS";
    public final String GameTurboResultingQueueName = "TURBO_RESULTING";

    //Freebet
    public final int FreebetRedeemed = 0;
    public final int FreebetPending = 1;
    public final int FreebetExpired = 3;

    public Basics() {

    }

}
