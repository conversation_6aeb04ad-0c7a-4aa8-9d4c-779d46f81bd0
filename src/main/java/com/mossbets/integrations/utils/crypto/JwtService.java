package com.mossbets.integrations.utils.crypto;

import com.mossbets.integrations.utils.Utilities;
import jakarta.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class JwtService {

    /**
     * ConvertToString
     *
     * @param hex
     * @return
     */
    public static String ConvertToString(String hex) {
        if (Utilities.isBlank(hex)) {
            return null;
        }

        StringBuilder output = new StringBuilder();
        for (int i = 0; i < hex.length(); i += 2) {
            String str = hex.substring(i, i + 2);
            output.append((char) Integer.parseInt(str, 16));
        }

        return output.toString();
    }

    /**
     * ConvertToHex
     *
     * @param str
     * @return
     */
    public static String ConvertToHex(String str) {
        StringBuilder hexStr = new StringBuilder();
        for (char c : str.toCharArray()) {
            String hex = Integer.toHexString(c);
            hexStr.append(hex);
        }
        return hexStr.toString();
    }
}
