package com.mossbets.integrations.utils.http;

import com.mossbets.integrations.utils.Utilities;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import com.google.gson.JsonObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class ApiHttpResponse {

    /**
     * sendError
     * @param httpCode
     * @param status
     * @param message
     * @param startTime
     * @return
     * @throws JSONException 
     */
    public static Response sendError(int httpCode, int status, String message,
            Instant startTime) throws Exception {
        JsonObject data = new JsonObject();
        data.addProperty("code", status);
        data.addProperty("message", message);

        JsonObject result = new JsonObject();
        result.addProperty("code", "Error");
        result.addProperty("statusDescription", "Request is not successful");
        result.add("data", data);

        return send(result, httpCode, startTime, true);
    }

    /**
     * Response
     *
     * Send CORS(Do not alter any parameters here unless instructed to)
     *
     * @param result
     * @param statusCode
     * @param startTime
     * @param logToFile
     * @return
     */
    public static Response send(JsonObject result, int statusCode, Instant startTime, boolean logToFile) {
        if (logToFile) {
            Logger.getLogger(ApiHttpResponse.class).info(Utilities.getLogPreString("Response")
                    + HttpReason(statusCode)
                    + "| Took " + Utilities.CalculateTAT(startTime) + " mSec(s)"
                    + "| " + result.toString());
        }

        return Response.status(statusCode, HttpReason(statusCode))
                .header("Status", statusCode + " " + HttpReason(statusCode).toUpperCase())
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("Content-Type", "application/json")
                .header("Accept-Charset", "utf-8")
                .header("Access-Control-Allow-Headers", "Access-Control-Allow-Headers"
                        + ",Origin,Accept,X-Requested-With,Content-Type,Authorization"
                        + ",Access-Control-Request-Method,Access-Control-Request-Headers"
                        + ",x-hash-key,x-requested-with,x-signature,x-api-key,x-access,x-app-key")
                .header("Access-Control-Expose-Headers", "Content-Length,X-JSON")
                .header("x-tat", Utilities.CalculateTAT(startTime) + " mSec(s)")
                .entity(result.toString())
                .build();
    }

    /**
     * HttpReason
     *
     * @param code
     * @return
     */
    private static String HttpReason(int code) {
        Map<Integer, String> arr = new HashMap<>();
        arr.put(200, "OK");
        arr.put(201, "CREATED");
        arr.put(202, "ACCEPTED");
        arr.put(204, "NO CONTENT");
        arr.put(304, "NOT MODIFIED");
        arr.put(400, "BAD REQUEST");
        arr.put(401, "UNAUTHORIZED REQUEST");
        arr.put(402, "PAYMENT REQUIRED");
        arr.put(403, "REQUEST FORBIDDEN");
        arr.put(404, "NOT FOUND");
        arr.put(408, "REQUEST TIMED-OUT");
        arr.put(405, "METHOD NOT ALLOWED");
        arr.put(421, "MISDIRECTED REQUEST");
        arr.put(422, "UNPROCESSABLE ENTITY");
        arr.put(429, "Too Many Requests");
        arr.put(500, "INTERNAL SERVER ERROR");
        arr.put(502, "BAD GATEWAY");
        arr.put(503, "Service Unavailable");
        arr.put(504, "Gateway Timeout");

        return arr.getOrDefault(code, "");
    }
}
