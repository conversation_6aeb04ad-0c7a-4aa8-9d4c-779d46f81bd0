package com.mossbets.integrations.utils;

import com.mossbets.integrations.utils.props.Props;
import com.rabbitmq.client.BuiltinExchangeType;
import com.rabbitmq.client.Channel;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import jakarta.enterprise.context.ApplicationScoped;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.concurrent.TimeoutException;

import jakarta.inject.Inject;
import org.jboss.logging.Logger;

@ApplicationScoped
public final class Queue {

    private static final Logger logger = Logger.getLogger(Queue.class);

    @Inject
    Props props;

    private final String QUEUE_SUFFIX = "_QUEUE";
    private final String ROUTE_SUFFIX = "_ROUTE";
    private final String EXCHANGE_SUFFIX = "_EXCHANGE";

    /**
     * publishMessage
     *
     * @param message
     * @param queueName
     * @param routingKey
     * @param exchangekey
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    @WithSpan
    public boolean publishMessage(String message, String queueName, String routingKey, String exchangekey) {
        Instant startTime = Instant.now();
        boolean status = false;
        try {
            String queue = props.rabbitMqPrefix().concat("_" + queueName.concat(QUEUE_SUFFIX));
            String route = props.rabbitMqPrefix().concat("_" + routingKey.concat(ROUTE_SUFFIX));
            String exchange = props.rabbitMqPrefix().concat("_" + exchangekey.concat(EXCHANGE_SUFFIX));

            com.rabbitmq.client.ConnectionFactory factory = new com.rabbitmq.client.ConnectionFactory();
            factory.setConnectionTimeout(props.rabbitMqTimeout());
            factory.setHost(props.rabbitMqHost());
            factory.setVirtualHost(props.rabbitMqVhost());
            factory.setUsername(props.rabbitMqUsername());
            factory.setPassword(props.rabbitMqPassword());
            factory.setPort(props.rabbitMqPort());



            logger.info(Utilities.getLogPreString("Queue")
            + "Took " + Utilities.CalculateTAT(startTime) + " mSec(s)"
            + "|Payload:" + message
            + "|User:" + props.rabbitMqUsername()
            + "|pass:" + props.rabbitMqPassword()
            + "|Published to Queue: {" + queue + "} Successfully");

            

            try (com.rabbitmq.client.Connection connection = factory.newConnection()) {
                Channel channel = connection.createChannel();
                channel.queueDeclare(queue, true, false, false, null);
                channel.exchangeDeclare(exchange,
                        BuiltinExchangeType.DIRECT, true, false, null);
                channel.basicPublish(exchange, route, null,
                        message.getBytes(StandardCharsets.UTF_8));
                channel.queueBind(queue, exchange, route);

                logger.info(Utilities.getLogPreString("Queue")
                        + "Took " + Utilities.CalculateTAT(startTime) + " mSec(s)"
                        + "|Payload:" + message
                        + "|User:" + props.rabbitMqUsername()
                        + "|pass:" + props.rabbitMqPassword()
                        + "|Published to Queue: {" + queue + "} Successfully");
                status = true;
            } catch (IOException | TimeoutException ex) {
                throw ex;
            }
        } catch (Exception e) {

            logger.error(Utilities.getLogPreString("Queue")
                    + "publishMessage()"
                    + "|Payload:" + message
                    + "|Queue: {" + queueName + "}"
                    + "|Exception: ", e);
        }

        return status;
    }
}
