package com.mossbets.integrations.utils;

import com.mossbets.integrations.utils.props.Basics;
import jakarta.enterprise.context.ApplicationScoped;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import javax.net.ssl.SSLContext;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class ApiCalls {

    @SuppressWarnings("UseSpecificCatch")
    public static Map<String, Object> sendHttpJsonPostData(String postUrl, String payload,
            Map<String, String> postHeaders, Basics config, Logger logger) {
        Instant startTime = Instant.now();
        postUrl = Utilities.CleanCallbackUrl(postUrl);

        if (!Utilities.ValidateUrl(postUrl)) {
            return buildHttpErrorResponse(400,
                    "URISyntaxException<>Invalid URL!", startTime);
        }

        // Set default headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Connection", "close");
        headers.put("Accept", "*/*");

        if (postHeaders != null) {
            headers.putAll(postHeaders);
        }

        logger.infof("%s URL: %s "
                + "| Headers: %s "
                + "| Request: %s",
                Utilities.getLogPreString("ApiCalls"),
                postUrl, headers, payload);

        int statusCode = 502;
        String responseContent = "";

        try {
            // Trust-all SSL context
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(null, (chain, authType) -> true)
                    .build();

            // Build request config
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(config.ConnectTimeOut)
                    .setSocketTimeout(config.SocketTimeOut)
                    .setContentCompressionEnabled(true)
                    .setExpectContinueEnabled(true)
                    .build();

            try (CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(sslContext)
                    .setDefaultRequestConfig(requestConfig)
                    .build()) {

                HttpPost postRequest = new HttpPost(postUrl);
                postRequest.setEntity(new StringEntity(payload, StandardCharsets.UTF_8));
                postRequest.setHeader("Content-Type", "application/json");

                headers.forEach(postRequest::addHeader);

                try (CloseableHttpResponse response = httpClient.execute(postRequest)) {
                    statusCode = response.getStatusLine().getStatusCode();
                    responseContent = EntityUtils.toString(response.getEntity());

                    return buildHttpSuccessResponse(statusCode, responseContent, startTime);
                }
            }
        } catch (Exception e) {
            logger.errorf("%s InitializePlayer()"
                    + "| URL:%s"
                    + "| Exception occurred: %s", Utilities.getLogPreString("ApiCalls"),
                    postUrl, e.getMessage());

            return buildHttpErrorResponse(statusCode, "Http Error: " + e.getMessage(), startTime);
        }
    }

    /**
     * BuildHttpSuccessResponse
     *
     * @param statusCode
     * @param response
     * @param startTime
     * @return
     */
    private static Map<String, Object> buildHttpSuccessResponse(int statusCode,
            String response, Instant startTime) {
        Map<String, Object> result = new HashMap<>();
        result.put("statusCode", statusCode);
        result.put("response", response);
        result.put("error", "");
        result.put("tat", Utilities.CalculateTAT(startTime));
        return result;
    }

    /**
     * BuildHttpErrorResponse
     *
     * @param statusCode
     * @param errorMessage
     * @param startTime
     * @return
     */
    private static Map<String, Object> buildHttpErrorResponse(int statusCode,
            String errorMessage, Instant startTime) {
        Map<String, Object> result = new HashMap<>();
        result.put("statusCode", statusCode);
        result.put("response", new JSONObject().toString());
        result.put("error", errorMessage);
        result.put("tat", Utilities.CalculateTAT(startTime));
        return result;
    }
}
