package com.mossbets.integrations.controllers.resources;

import com.mossbets.integrations.utils.Utilities;
import io.agroal.api.AgroalDataSource;
import io.quarkus.agroal.DataSource;
import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import org.jboss.logging.Logger;

@Startup
@ApplicationScoped
public class DB {

    @Inject
    @DataSource("dbr1")
    private AgroalDataSource dbReadB2b;

    @Inject
    @DataSource("dbr2")
    private AgroalDataSource dbTrxRead;

    @Inject
    @DataSource("dbr3")
    private AgroalDataSource dbReadB2bVirtuals;

     @Inject
     @DataSource("dbr4")
     private AgroalDataSource dbReadBonus;

    // @Inject
    // @DataSource("dbr5")
    // private AgroalDataSource dbReadB2bJp;

    // @Inject
    // @DataSource("dbr6")
    // private AgroalDataSource dbReadB2bRacing;

    // @Inject
    // @DataSource("dbr7")
    // private AgroalDataSource dbReadB2bTurbo;

    // @Inject
    // @DataSource("dbr8")
    // private AgroalDataSource dbReadB2bBox;

    // @Inject
    // @DataSource("dbr9")
    // private AgroalDataSource dbReadB2bLotto;

    @Inject
    @DataSource("db1")
    private AgroalDataSource dbMainWrite;

    @Inject
    @DataSource("db2")
    private AgroalDataSource dbWriteTrx;

    @Inject
    @DataSource("db3")
    private AgroalDataSource dbWriteVirtuals;

    // @Inject
    // @DataSource("db4")
    // private AgroalDataSource dbWriteTona;

    // @Inject
    // @DataSource("db3")
    // private AgroalDataSource dbWriteJackpot;

    // @Inject
    // @DataSource("db6")
    // private AgroalDataSource dbWriteRacing;

    // @Inject
    // @DataSource("db7")
    // private AgroalDataSource dbWriteTurbo;

    // @Inject
    // @DataSource("db8")
    // private AgroalDataSource dbWriteBox;

    // @Inject
    // @DataSource("db9")
    // private AgroalDataSource dbWriteLotto;

    @Inject
    Logger logger;

    @Inject
    ExecutorService sysExecutor;

    private Map<String, AgroalDataSource> readDb;
    private Map<String, AgroalDataSource> writeDb;

    @PostConstruct
    void init() {
        readDb = Map.of(
                "main", dbReadB2b,
                "trxn", dbTrxRead,
                "virtual", dbReadB2bVirtuals,
                "dbbonus", dbReadBonus
                // "jp", dbReadB2bJp,
                // "racing", dbReadB2bRacing,
                // "turbo", dbReadB2bTurbo,
                // "box", dbReadB2bBox,
                // "lotto", dbReadB2bLotto
        );

        writeDb = Map.of(
                "main", dbMainWrite,
                "trxn", dbWriteTrx,
                "virtual", dbWriteVirtuals,
                "dbbonus", dbReadBonus
                // "jp", dbWriteJackpot,
                // "racing", dbWriteRacing,
                // "turbo", dbWriteTurbo,
                // "box", dbWriteBox,
                // "lotto", dbWriteLotto
        );
    }

    /**
     * pick
     *
     * @param map
     * @param name
     * @param fallback
     * @return
     */
    private AgroalDataSource pick(Map<String, AgroalDataSource> map, String name,
            AgroalDataSource fallback) {
        if (name == null || name.isBlank()) {
            logger.warnf(Utilities.getLogPreString("DB")
                    + "pick()"
                    + "|DataSource name is null or blank, using default DataSource");
            return fallback;
        }

        AgroalDataSource ds = map.get(name.toLowerCase());
        return ds;
    }

    /**
     * getReadDB
     *
     * @param name
     * @return
     */
    public AgroalDataSource getReadDB(String name) {
        return pick(readDb, name, dbReadB2b);
    }

    /**
     * getWriteDB
     *
     * @param name
     * @return
     */
    public AgroalDataSource getWriteDB(String name) {
        return pick(writeDb, name, dbMainWrite);
    }

    /**
     * executeQuery
     *
     * @param <T>
     * @param dsName
     * @param sql
     * @param params
     * @param mapper
     * @return
     */
    public <T> List<T> executeQuery(String dsName, String sql, List<Object> params,
            Function<ResultSet, T> mapper) {
        if (sql == null) {
            throw new IllegalArgumentException("SQL query cannot be null");
        }
        if (mapper == null) {
            throw new IllegalArgumentException("Mapper function cannot be null");
        }
        return execute(dsName, sql, params, mapper, false);
    }

    /**
     * executeQueryAsync
     *
     * @param <T>
     * @param dsName
     * @param sql
     * @param params
     * @param mapper
     * @return
     */
    public <T> CompletionStage<List<T>> executeQueryAsync(String dsName, String sql,
            List<Object> params, Function<ResultSet, T> mapper) {
        if (sql == null) {
            throw new IllegalArgumentException("SQL query cannot be null");
        }
        if (mapper == null) {
            throw new IllegalArgumentException("Mapper function cannot be null");
        }
        return CompletableFuture.supplyAsync(() -> executeQuery(dsName, sql, params, mapper),
                sysExecutor);
    }

    /**
     *
     * @param dsName
     * @param sql
     * @param params
     * @return
     */
    public int executeUpdate(String dsName, String sql, List<Object> params) {
        if (sql == null) {
            throw new IllegalArgumentException("SQL query cannot be null");
        }
        try (Connection conn = getWriteDB(dsName).getConnection();
                PreparedStatement ps = conn.prepareStatement(sql)) {
            if (params != null) {
                for (int i = 0; i < params.size(); i++) {
                    ps.setObject(i + 1, params.get(i));
                }
            }
            int rows = ps.executeUpdate();
            logger.debugf(Utilities.getLogPreString("DB")
                    + "executeUpdate()"
                    + "|[%s] Update executed: %d rows affected", dsName, rows);
            return rows;
        } catch (SQLException e) {
            logger.errorf(e, Utilities.getLogPreString("DB")
                    + "executeUpdate()"
                    + "|[%s] Update failed: %s", dsName, sql);
            throw new RuntimeException("Update failed on " + dsName + ": " + e.getMessage(), e);
        }
    }

    /**
     * execute
     *
     * @param <T>
     * @param dsName
     * @param sql
     * @param params
     * @param mapper
     * @param isWrite
     * @return
     */
    private <T> List<T> execute(String dsName, String sql, List<Object> params,
            Function<ResultSet, T> mapper, boolean isWrite) {
        List<T> list = new ArrayList<>();
        try (Connection conn = (isWrite ? getWriteDB(dsName) : getReadDB(dsName)).getConnection();
                PreparedStatement ps = conn.prepareStatement(sql)) {
            if (params != null) {
                for (int i = 0; i < params.size(); i++) {
                    ps.setObject(i + 1, params.get(i));
                }
            }
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    list.add(mapper.apply(rs));
                }
            }
            return list;
        } catch (SQLException e) {
            logger.errorf(e, Utilities.getLogPreString("DB")
                    + "execute()"
                    + "|[%s] Query failed: %s", dsName, sql);
            throw new RuntimeException("Query failed on " + dsName + ": " + e.getMessage(), e);
        }
    }

    public Optional<Map<String, Object>> rawSelectOne(String dsName, String sql, Map<String, Object> params) {
        if (sql == null) {
            throw new IllegalArgumentException("SQL query cannot be null");
        }
        // Convert named parameters to positional parameters
        List<Object> positionalParams = new ArrayList<>();
        String processedSql = convertNamedToPositionalParams(sql, params, positionalParams);

        try (Connection conn = getReadDB(dsName).getConnection();
             PreparedStatement ps = conn.prepareStatement(processedSql)) {
            for (int i = 0; i < positionalParams.size(); i++) {
                ps.setObject(i + 1, positionalParams.get(i));
            }
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    Map<String, Object> result = new HashMap<>();
                    for (int i = 1; i <= rs.getMetaData().getColumnCount(); i++) {
                        result.put(rs.getMetaData().getColumnLabel(i), rs.getObject(i));
                    }
                    return Optional.of(result);
                }
                return Optional.empty();
            }
        } catch (SQLException e) {
            logger.errorf(e, Utilities.getLogPreString("DB") + "rawSelectOne()|[%s] Query failed: %s", dsName, sql);
            throw new RuntimeException("Query failed on " + dsName + ": " + e.getMessage(), e);
        }
    }

    public long insert(String dsName, String table, Map<String, Object> data) {
        if (table == null || table.isBlank()) {
            throw new IllegalArgumentException("Table name cannot be null or blank");
        }
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("Data map cannot be null or empty");
        }

        String columns = String.join(", ", data.keySet());
        String placeholders = String.join(", ", data.keySet().stream().map(k -> "?").toArray(String[]::new));
        String sql = String.format("INSERT INTO %s (%s) VALUES (%s)", table, columns, placeholders);

        try (Connection conn = getWriteDB(dsName).getConnection();
             PreparedStatement ps = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            int index = 1;
            for (Object value : data.values()) {
                ps.setObject(index++, value);
            }
            int rows = ps.executeUpdate();
            if (rows == 0) {
                logger.info("Failed to create leader board entry");
                throw new SQLException("Insert failed, no rows affected");
            }
            try (ResultSet rs = ps.getGeneratedKeys()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
                throw new SQLException("Insert failed, no generated key returned");
            }
        } catch (SQLException e) {
            logger.errorf(e, Utilities.getLogPreString("DB") + "insert()|[%s] Insert failed into %s", dsName, table);
            throw new RuntimeException("Insert failed on " + dsName + ": " + e.getMessage(), e);
        }
    }

    private String convertNamedToPositionalParams(String sql, Map<String, Object> params, List<Object> positionalParams) {
        if (params == null || params.isEmpty()) {
            return sql;
        }
        String result = sql;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            result = result.replace(":" + entry.getKey(), "?");
            positionalParams.add(entry.getValue());
        }
        return result;
    }
}
