package com.mossbets.integrations.controllers.resources;

import com.mossbets.integrations.utils.props.Basics;
import io.quarkus.runtime.Startup;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

@Startup
@ApplicationScoped
public class Configurations {

    @Inject
    public Logger logger;

    @Inject
    public Basics basics = new Basics();
}
