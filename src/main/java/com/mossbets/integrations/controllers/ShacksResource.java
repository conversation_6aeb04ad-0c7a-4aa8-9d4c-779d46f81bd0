package com.mossbets.integrations.controllers;
import java.util.*;

import com.mossbets.integrations.config.TransactionConstants;
import com.mossbets.integrations.controllers.resources.Configurations;
import com.mossbets.integrations.controllers.resources.DB;
import com.mossbets.integrations.utils.Utilities;

import com.mossbets.integrations.utils.WalletUtils;
import com.mossbets.integrations.utils.crypto.HmacService;
import com.mossbets.integrations.utils.props.LeaderboardService;
import com.mossbets.integrations.utils.props.Props;

import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;

import java.math.BigDecimal;
import java.time.Instant;

@Path("/she/v1/")
@Produces(MediaType.APPLICATION_JSON)
@ApplicationScoped
public class ShacksResource extends Configurations{

    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(ShacksResource.class);
    @Inject
    private WalletUtils WalletUtils;
    @Inject
    private Props props;
    @Inject
    HmacService hmacService;
    @Inject
    LeaderboardService leaderboardService;

    @Inject
    public DB dbSource;

    /**
     * InitiateGamePlay
     *
     * @param headers
     * @param rawRequest
     * @return
     */
    @POST
    @Path("launch")
    @Consumes(MediaType.APPLICATION_JSON)
    @WithSpan
    public Response InitiateGamePlay(@Context HttpHeaders headers, String rawRequest) {
        Instant startTime = Instant.now();
        String appKey = headers.getHeaderString("x-app-key");
        String accessKey = headers.getHeaderString("x-access-key");
        logger.info(Utilities.getLogPreString("InitiateAPI")
        + "Initialize()"
        + "|Headers: " + appKey  + ", " + accessKey );

        if (appKey == null || accessKey == null) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Missing headers").build();
        }

        try {

            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> requestData = objectMapper.readValue(rawRequest, Map.class);
            int mode = Optional.ofNullable(requestData.get("mode")).map(val -> (val instanceof Number) ? ((Number) val).intValue() : Integer.parseInt(val.toString())).orElse(0);
            String game_id = Optional.ofNullable(requestData.get("game_id")).map(Object::toString).orElse(null);
            String baseUrl = TransactionConstants.SHACKS_BASE_URL;
    
            if (mode == 1) {
                String sessionToken = "";
                Map<String, Object> authResults = null;
                if (mode == 1 || accessKey != null) {
                    authResults = Utilities.authenticateAccessKey(accessKey,dbSource.getWriteDB("main").getConnection());
                    if ((int) authResults.get("code") != 200) {
                        return Response.status((int) authResults.get("code")).entity((String) authResults.get("message")).build();
                    }
                    sessionToken = Utilities.encrypt(authResults.get("data.profile_id").toString());
                }
                baseUrl = TransactionConstants.SHACKS_BASE_URL + game_id + "?"+"client_id="+sessionToken;
            } else {
                baseUrl = TransactionConstants.SHACKS_BASE_URL + game_id;
            }

            JsonObject response = new JsonObject();
            response.addProperty("code", 200);
            response.addProperty("ur",baseUrl);
            response.addProperty("message", "Game launched successfully");
            return Response.ok(response.toString()).build();
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("InitiateAPI")
                    + "Initialize()"
                    + "|Failed to parse JSON: " + e.getMessage());
            return sendError(2, false, startTime);
        }
    }


    @WithSpan
    @Path("/GetPlayerDetails")
    @GET
    @Transactional
    public Response getPlayerDetails(@Context HttpHeaders headers)  {
        Instant startTime = Instant.now();
        String userId = headers.getHeaderString("playerId");
        String game_type = headers.getHeaderString("gameType");
        String session_id = headers.getHeaderString("sessionId");
        String signature = headers.getHeaderString("signature");
        try {
            if (userId == null || game_type == null || session_id == null || signature == null) {
                return Response.status(Response.Status.BAD_REQUEST).entity("Missing headers").build();
            }

            Map<String, Object> balance = WalletUtils.getPlayerBalance(userId);
            String newBalance = hmacService.formatBalanceToTwoDecimals(balance.get("balance").toString());

            JsonObject response = new JsonObject();
            response.addProperty("playerId", userId);
            response.addProperty("username", balance.get("name").toString());
            response.addProperty("currency", props.evoCurrency());
            response.addProperty("balance", newBalance);
            response.addProperty("processed", true);
            response.addProperty("code", 1);

            logger.info(Utilities.getLogPreString("BalanceAPI")
                    + "User : " + userId + " User response is : " + response
                    + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
            return Response.ok(response.toString()).build();

        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("BalanceAPI")
                    + "Took " + Utilities.CalculateTAT(startTime) + " mSec(s)"
                    + " | Exception: ", e);
            return sendError(2, false,startTime);
        }
    }
    @POST
    @Path("/debit")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @WithSpan
    public Response debit(JsonObject request) {
        Instant startTime = Instant.now();
        String userId = request.get("playerId").getAsString();
        try {
            if (!request.has("type") || !request.has("playerId") ||
                    !request.has("userid") || !request.has("playerId") ||
                    !request.has("gameRoundEnded") || !request.has("gameType") ||
                    !request.has("roundId") || !request.has("freebet") ||
                    !request.has("amount") || !request.has("reference") ||
                    !request.has("sessionId") || !request.has("signature")){
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed - Missing parameters. User: " + userId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(2, false, startTime);
            }

            String requestType = request.get("type").getAsString();
            if (!"debit".equals(requestType)) {
                return sendError(2, false, startTime);
            }

            String type = request.get("type").getAsString();
            String amountStr = request.get("amount").getAsString();
            String roundId = request.get("roundId").getAsString();
            String gameType = request.get("gameType").getAsString();
            String reference = request.get("reference").getAsString();
            String sessionId = request.get("sessionId").getAsString();


            if (WalletUtils.getPlayerBalance(userId)==null || WalletUtils.getPlayerBalance(userId).isEmpty()){
                return sendError(2, false, startTime);
            }

            BigDecimal amount;
            try {
                amount = new BigDecimal(amountStr);
                if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                    logger.info(Utilities.getLogPreString("Debit")
                            + "Request failed. User: " + userId + "Round Id" + roundId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                    return sendError(2, false, startTime);
                }
            } catch (NumberFormatException e) {
                logger.info(Utilities.getLogPreString("Debit")
                        + "Request failed. User: " + userId + "Round Id" + roundId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(2, false, startTime);
            }
            // Check for rollback scenarios in debit
            boolean isCancellation = request.has("subtype") && "cancel".equals(request.get("subtype").getAsString());

            String transactionId = null;

            Map<String, Object> balanceInfo = WalletUtils.getPlayerBalance(userId);
            String currentBalance = balanceInfo.get("balance").toString();

            if (isCancellation) {
                transactionId = WalletUtils.processCancellation(type, userId, roundId, reference, amount, request);
                if (transactionId == null) {
                    logger.info(Utilities.getLogPreString("Debit - Cancellation")
                            + "Request failed. User: " + userId + "Game Id" + roundId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                    return sendError(2, false, startTime);
                }
            } else {
                String referenceParams = roundId + '|' + reference +'|' + sessionId;
                  
                int duplicate = WalletUtils.isDuplicateAction("debit", roundId,(reference),
                        userId, startTime, amount);
                  
                logger.info("Duplicate check " + sessionId + ", Action ID: " + reference + "duplicate: " + duplicate);

                if(duplicate==1){
                    logger.info("Duplicate transaction detected for Tid: " + sessionId + ", Action ID: " + reference);
                    return sendError(2, false, startTime);
                }else if(duplicate==2){
                    logger.info("Duplicate transaction detected for Action: " + sessionId + ", Action ID: " + reference);
                    return successResponse( userId, startTime);
                }
                BigDecimal currentBalanceBD = new BigDecimal(currentBalance);
                if (currentBalanceBD.compareTo(amount) < 0) {
                    logger.info(Utilities.getLogPreString("Debit")
                            + "Request failed - Insufficient funds. User: " + userId + "Game Id" + roundId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                    return sendError(2, false, startTime);
                }

                request.addProperty("ip_address", Utilities.getClientIpAddress());
                   
                transactionId = WalletUtils.processDebit(userId, roundId,sessionId, reference,
                            amount,reference, "Debit for game: " + gameType, gameType, request);
                if(leaderboardService.createLeaderboardEntry(userId, amount.doubleValue())){
                    logger.info("Successfully created or updated leaderboard entry for user: " + userId);
                }
                if (transactionId == null) {
                    logger.info(Utilities.getLogPreString("Debit")
                            + "Request failed. User: " + userId + "Game Id" + roundId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                    return sendError(2, false, startTime);
                }
            }

            logger.info(Utilities.getLogPreString("Debit")
                    + "Request successful. User: " + userId + "Game Id" + roundId
                    + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");

                // Create a JSON response
            return successResponse( userId, startTime);
        } catch (Exception e) {
            logger.info(Utilities.getLogPreString("Debit")
                    + "Request failed. User: " + userId
                    + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
            return sendError(2, false, startTime);
        }
    }

    @POST
    @Path("credit")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @WithSpan
    public Response credit(JsonObject request) {
        Instant startTime = Instant.now();
        String userId = request.get("playerId").getAsString();
        try {
            // Validate required fields
            if (!request.has("type") || !request.has("playerId") ||
                    !request.has("userid") || !request.has("playerId") ||
                    !request.has("gameRoundEnded") || !request.has("gameType") ||
                    !request.has("roundId") || !request.has("freebet") ||
                    !request.has("amount") || !request.has("reference") ||
                    !request.has("sessionId") || !request.has("signature")){
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed - Missing parameters. User: " + userId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(2, false, startTime);
            }

            String requestType = request.get("type").getAsString();
            if (!"credit".equals(requestType)) {
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed - Missing parameters. User: " + userId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(2, false, startTime);
            }

            String result = request.get("result").getAsString();
            String type = request.get("type").getAsString();
            String gameOutcome = request.get("gameOutcome").getAsString();
            String gameRoundEnded = request.get("gameRoundEnded").getAsString();
            String amountStr = request.get("amount").getAsString();
            String roundId = request.get("roundId").getAsString();
            String freebet = request.get("freebet").getAsString();
            String gameType = request.get("gameType").getAsString();
            String reference = request.get("reference").getAsString();
            String sessionId = request.get("sessionId").getAsString();
            String currency = request.get("currency").getAsString();


            logger.info("User ID: " + WalletUtils.getInfo(userId));
            if (!WalletUtils.getInfo(userId)){
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed - Invalid user. User: " + userId + "Game Id" + roundId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(2, false, startTime);
            }
            BigDecimal amount;
            try {
                amount = new BigDecimal(amountStr);
                if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                    logger.info(Utilities.getLogPreString("Credit")
                            + "Request failed - Invalid amount. User: " + userId + "Game Id" + roundId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                    return sendError(2, false, startTime);
                }
            } catch (NumberFormatException e) {
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed - Invalid amount. User: " + userId + "Game Id" + roundId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(2, false, startTime);
            }


                // Check for rollback scenarios
            boolean isRollback = request.has("i_rollback");
            boolean isPromotion = request.has("subtype") && "promotion".equals(request.get("subtype").getAsString());;
            boolean isCancellation = request.has("subtype") && "cancel".equals(request.get("subtype").getAsString());

            String transactionId = null;

            if (isRollback) {
                // Type 1: Rollback with i_rollback field
                String rollbackTid = request.get("i_rollback").getAsString();
                logger.info("Processing rollback for Reference: " + rollbackTid + ", New reference: " +  reference);

                transactionId = WalletUtils.processRollback(rollbackTid, reference, userId, roundId, amount, request);

                if (transactionId == null) {
                    return successResponse(userId, startTime);
                }

            } else if (isCancellation) {
                    // Type 2: Cancellation with subtype="cancel"
                logger.info("Processing cancellation for Reference: " + reference);

                transactionId = WalletUtils.processCancellation(type, reference, userId, roundId,  amount, request);

                logger.info("Transaction ID: " + transactionId);
                if (transactionId == null) {
                    return sendError(2, false, startTime);
                }

            }
            else if (isPromotion){

            }else {
                // Regular credit processing
                int duplicate = WalletUtils.isDuplicateTransaction("credit", userId,reference, startTime, amount);
                
                logger.info("Duplicate check" + reference  + "duplicate: " + duplicate);
                
                if(duplicate==1){
                    logger.info(Utilities.getLogPreString("Credit")
                            + "Request failed - Duplicate transaction. User: " + userId + "Game Id" + roundId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                        return successResponse( userId, startTime);
                }else if(duplicate==2){
                    logger.info(Utilities.getLogPreString("Credit")
                            + "Request failed - Duplicate transaction. User: " + userId + "Game Id" + roundId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                    return sendError(2, false, startTime);
                }

                // Add IP address to the request
                logger.info("passed transaction: " + reference );
                request.addProperty("ip_address", Utilities.getClientIpAddress());


                // Process the credit
                transactionId = WalletUtils.updatePlayerBalance(
                        userId,
                        roundId,
                        amount.doubleValue(),
                        request,
                        String.valueOf(System.currentTimeMillis()),
                        "CREDIT"
                );

                if (transactionId == null) {
                    logger.info(Utilities.getLogPreString("Credit")
                            + "Request failed - Duplicate transaction. User: " + userId + "Game Id" + roundId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                    return successResponse( userId, startTime);
                }
            }

            return successResponse( userId, startTime);

        } catch (Exception e) {
            logger.error("Error processing Credit request", e);
            return sendError(2, false, startTime);
        }
    }
   @POST
   @Path("ping")
   @Consumes(MediaType.APPLICATION_JSON)
   @Produces(MediaType.APPLICATION_JSON)
   @WithSpan
   public Response handlePing(@Context HttpHeaders headers, JsonObject request) {
        Instant startTime = Instant.now();
        try {

            // Create a JSON builder and build the response
            JsonObject response = new JsonObject();
            response.addProperty("status", "OK");
            Map<String, Object> message = new HashMap<>();
            message.put("status", "OK");

            logger.info(Utilities.getLogPreString("PingAPI")
                    + "Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
            return Response.ok(response.toString()).build();

        } catch (Exception e) {
                logger.error(Utilities.getLogPreString("PingAPI")
                        + "Took " + Utilities.CalculateTAT(startTime) + " mSec(s)"
                        + "| Exception:", e);
                return sendError(2, false, startTime);
        }
    }

    private Response sendError(int errorCode, boolean message, Instant startTime) {
        JsonObject errorResponse = new JsonObject();
        errorResponse.addProperty("message", message);
        errorResponse.addProperty("code", errorCode);

        logger.error(Utilities.getLogPreString("APIError")
                + " | Code: " + errorCode
                + " | Message: " + message
                + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");

        return Response.ok()
                .entity(errorResponse.toString())
                .type(MediaType.APPLICATION_JSON)
                .build();
    }
    public Response successResponse(String userId, Instant startTime) {
            Map<String, Object> balanceInfo2 = WalletUtils.getPlayerBalance(userId);
            String newBalance2 = balanceInfo2.get("balance").toString();

            JsonObject response = new JsonObject();
            response.addProperty("processed", true);
            response.addProperty("code", 2);
            response.addProperty("balance", newBalance2);
            response.addProperty("currency", props.evoCurrency());;

            return Response.ok(response.toString()).build();
        }
}
