package com.mossbets.integrations.controllers.middleware;

import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;
import org.jboss.logging.Logger;

@Provider
public class CustomExceptionMapper implements ExceptionMapper<RuntimeException> {

    private static final Logger log = Logger.getLogger(CustomExceptionMapper.class);

    @Override
    public Response toResponse(RuntimeException ex) {
        log.error("CustomExceptionMapper Unhandled exception in router", ex);
        return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("Content-Type", "application/json")
                .header("Accept-Charset", "utf-8")
                .header("Access-Control-Allow-Headers", "Access-Control-Allow-Headers"
                        + ",Origin,Accept,X-Requested-With,Content-Type,Authorization"
                        + ",Access-Control-Request-Method,Access-Control-Request-Headers"
                        + ",x-hash-key,x-requested-with,x-signature,x-api-key,x-access,x-app-key")
                .header("Access-Control-Expose-Headers", "Content-Length,X-JSON")
                .entity("{\"code\":\"Error\","
                        + "\"statusDescription\":\"Request is not successful\","
                        + "\"data\":{\"code\":500,\"message\":\"An internal error occurred\"}}")
                .build();
    }
}
