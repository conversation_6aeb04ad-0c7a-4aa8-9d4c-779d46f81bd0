package com.mossbets.integrations.controllers.middleware;

import jakarta.annotation.Priority;
import jakarta.ws.rs.NotFoundException;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;
import com.google.gson.JsonObject;

@Provider
@Priority(Priorities.USER)
public class NotFoundExceptionMapper implements ExceptionMapper<NotFoundException> {

    /**
     * NotFoundResponse
     *
     * @param e
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    @Override
    public Response toResponse(NotFoundException e) {
        String result;
        try {
            JsonObject data = new JsonObject();
            data.addProperty("code", 404);
            data.addProperty("message", "The route you are looking for is NOT found.");

            JsonObject response = new JsonObject();
            response.addProperty("code", "Error");
            response.addProperty("statusDescription", "Request is not successful");
            response.add("data", data);

            result = response.toString();
        } catch (Exception ex) {
            result = "{\"code\":\"Error\""
                    + ",\"statusDescription\":\"Request is not successful\""
                    + ",\"data\":{\"code\":404"
                    + ",\"message\":\"The route you are looking for is NOT found.\"}}";
        }

        return Response.status(404, "NOT FOUND")
                .header("Access-Control-Allow-Origin", "*")
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("Content-Type", "application/json")
                .entity(result)
                .build();
    }
}
