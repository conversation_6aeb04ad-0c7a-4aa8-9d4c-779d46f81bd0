package com.mossbets.integrations.config;

/**
 * Application-wide constants including transaction types, statuses, and configuration values.
 * All constants are organized in a type-safe and easily maintainable way.
 */
public final class TransactionConstants {
    // ========== APPLICATION CONFIG ==========
    public static final String SHACKS_PORTAL_NAME = "mossbets";
    public static final String SHACKS_SECRET_KEY = "0fb1f6c2b781990ff36a8409d4b049432036a7d10f38a237d8aa00d228b8a9c144672d80c59f7d6dbe0b2e70";
    public static final String SHACKS_PUBLIC_KEY = "1596b19a1e0ded5c8e7d885bf00547c7c35dd76fe66b20c0902f7503819768e9abe607c5ec2457e97acc15e0";
    public static final String SHACKS_BASE_URL = "https://game.shacksevo.co/staging/";
    public static final String APP_PATH = System.getProperty("user.dir");

    // ========== TRANSACTION TYPES ==========
    public static final int TYPE_CREDIT = 1;
    public static final int TYPE_DEBIT = 2;
    public static final int TYPE_REVERSAL = 3;
    public static final int TYPE_VOIDED = 4;
    public static final int TYPE_TAXES = 5;
    public static final int TYPE_BONUS = 6;
    public static final int TYPE_REFUND = 7;

    // ========== STATUS CODES ==========
    public static final class Status {
        // Bet and Betslip Statuses
        public static final int PENDING = 0;
        public static final int WON = 1;
        public static final int LOST = 3;
        public static final int VOIDED = 7;
        public static final int CANCELLED = 9;

        // Game Statuses
        public static final class Game {
            public static final int INACTIVE = 0;
            public static final int ACTIVE = 1;
            public static final int MAINTENANCE = 4;
        }

        // Bonus Statuses
        public static final class Bonus {
            public static final int ACTIVE = 1;
            public static final int REDEEMED = 2;
            public static final int ISSUED_CONDITION_PENDING = 7;
            public static final int EXPIRED = 10;
        }
    }

    // ========== WITHDRAWAL CONFIG ==========
    public static final class Withdrawal {
        public static final int MAX_WITHDRAWAL = 100000;

        public static final class Mpesa {
            public static final int MIN = 100;
            public static final int MAX = 100000;
        }
    }

    // ========== REFERENCE TYPES ==========
    public static final class ReferenceType {
        public static final int MPESA_WITHDRAWAL = 1;
        public static final int MPESA_DEPOSIT = 2;
        public static final int MPESA_WITHDRAWAL_REVERSAL = 3;

        public static final class Wallet {
            public static final int WITHDRAWAL = 1;
            public static final int DEPOSIT = 2;
            public static final int REVERSAL = 3;
        }

        public static final class Bet {
            public static final class Sports {
                public static final int WINNING = 5;
                public static final int VOIDED = 6;

                public static final class Stake {
                    public static final int CASH = 4;
                    public static final int BONUS = 26;
                    public static final int FREEBET = 27;
                }
            }

            public static final class Virtuals {
                public static final int STAKE = 7;
                public static final int WINNING = 8;
                public static final int VOIDED = 9;
            }

            public static final class SHACKSGAMES {
                public static final int STAKE = 10; // Example value, adjust as needed
                public static final int ROLLBACK = 11; // Rollback reference type
            }
        }
    }

    // ========== TAXES ==========
    public static final class Taxes {
        public static final int EXCISE_TAX = 15;    // 15%
        public static final int WITHHOLDING_TAX = 20; // 20%
    }

    // ========== PROVIDER NAMES ==========
    public static final String PROVIDER_SHACKSGAMES = "SHACKSGAMES";

    // ========== CURRENCIES ==========
    public static final String CURRENCY_KES = "KES";

    // ========== TRANSACTION SOURCES ==========
    public static final String SOURCE_SHACKSGAMES_CASH_BET = PROVIDER_SHACKSGAMES + "_CASH_BET";
    public static final String SOURCE_SHACKSGAMES_ROLLBACK = PROVIDER_SHACKSGAMES + "_ROLLBACK";
    public static final String SOURCE_SHACKSGAMES_CANCEL = PROVIDER_SHACKSGAMES + "_CANCEL";

    // ========== BATCH PROCESSING ==========
    public static final int QUERY_TRX_PROCESSING_BATCH_SIZE = 300;

    // ========== HELPER METHODS ==========
    public static String getBetDebitDescription(String gameId) {
        return gameId + " Bet Debit";
    }

    // Prevent instantiation
    private TransactionConstants() {}
}
