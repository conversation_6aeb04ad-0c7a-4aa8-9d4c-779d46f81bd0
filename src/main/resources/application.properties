#App
quarkus.application.version=1.0.1

#HTTP Apache
quarkus.http.port=8090
quarkus.http.host=0.0.0.0
quarkus.http.http2=true
quarkus.http.enable-compression=true
quarkus.http.limits.max-connections=1000
quarkus.http.limits.max-body-size = 2M

#CORS(Do not alter any parameters here unless instructed to)
quarkus.http.cors=true
quarkus.http.cors.origins=/.*/
quarkus.http.cors.methods=GET,POST
quarkus.http.cors.access-control-max-age=24H
quarkus.http.cors.access-control-allow-credentials=true
quarkus.http.cors.headers=accept,authorization,content-type,x-hash-key,x-requested-with,x-signature,x-api-key,x-access,x-app-key,x-service-key,x-timezone,x-timestamp,xmlhttprequest,access-control-allow-origin

#Headers
quarkus.http.header."Connection".value=keep-alive,Keep-Alive
quarkus.http.header."Keep-Alive".value=timeout=5,max=500
#quarkus.http.header."Transfer-Encoding".value=chunked
quarkus.http.header."X-Content-Type-Options".value=nosniff
quarkus.http.header."Access-Control-Allow-Methods".value=GET,POST,OPTIONS
quarkus.http.header."Access-Control-Request-Headers".value=Authorization,Origin,Content-Type,Accept,Content-Disposition

# Other Performance Optimizations
quarkus.thread-pool.core-threads=100 
quarkus.thread-pool.max-threads=200
quarkus.thread-pool.queue-size=1024

# Metrics and Health Checks
quarkus.datasource.metrics.enabled=true

#
#Datasource MASTER
#

#Main
quarkus.datasource."db1".db-kind=mysql
quarkus.datasource."db1".username=apps_user
quarkus.datasource."db1".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."db1".jdbc.initial-size=10
quarkus.datasource."db1".jdbc.max-size=50
quarkus.datasource."db1".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db1".jdbc.url=***********************************************************************************************************************************************************************************************************************************************************************************************
quarkus.datasource."db1".jdbc.validation-query-sql=select 1
quarkus.datasource."db1".jdbc.pooling-enabled=true
quarkus.datasource."db1".jdbc.min-size=1
quarkus.datasource."db1".jdbc.idle-removal-interval=60S
quarkus.datasource."db1".jdbc.background-validation-interval=10
quarkus.datasource."db1".jdbc.leak-detection-interval=30
quarkus.datasource."db1".jdbc.acquisition-timeout=10
quarkus.datasource."db1".jdbc.max-lifetime=1800S

#Bets
quarkus.datasource."db3".db-kind=mysql
quarkus.datasource."db3".username=apps_user
quarkus.datasource."db3".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."db3".jdbc.initial-size=10
quarkus.datasource."db3".jdbc.max-size=50
quarkus.datasource."db3".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db3".jdbc.url=********************************************************************************************************************************************************************************************************************************************************************************************
quarkus.datasource."db3".jdbc.validation-query-sql=select 1
quarkus.datasource."db3".jdbc.pooling-enabled=true
quarkus.datasource."db3".jdbc.min-size=1
quarkus.datasource."db3".jdbc.idle-removal-interval=60S
quarkus.datasource."db3".jdbc.background-validation-interval=10
quarkus.datasource."db3".jdbc.leak-detection-interval=30
quarkus.datasource."db3".jdbc.acquisition-timeout=10
quarkus.datasource."db3".jdbc.max-lifetime=1800S

#Transactions
quarkus.datasource."db2".db-kind=mysql
quarkus.datasource."db2".username=apps_user
quarkus.datasource."db2".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."db2".jdbc.initial-size=10
quarkus.datasource."db2".jdbc.max-size=50
quarkus.datasource."db2".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db2".jdbc.url=****************************************************************************************************************************************************************************************************************************************************************************************************
quarkus.datasource."db2".jdbc.validation-query-sql=select 1
quarkus.datasource."db2".jdbc.pooling-enabled=true
quarkus.datasource."db2".jdbc.min-size=1
quarkus.datasource."db2".jdbc.idle-removal-interval=60S
quarkus.datasource."db2".jdbc.background-validation-interval=10
quarkus.datasource."db2".jdbc.leak-detection-interval=30
quarkus.datasource."db2".jdbc.acquisition-timeout=10
quarkus.datasource."db2".jdbc.max-lifetime=1800S

#
#Datasource SLAVES
#
quarkus.datasource."dbr1".db-kind=mysql
quarkus.datasource."dbr1".username=apps_user
quarkus.datasource."dbr1".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."dbr1".jdbc.initial-size=5
quarkus.datasource."dbr1".jdbc.max-size=100
quarkus.datasource."dbr1".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr1".jdbc.url=*******************************************************************************************************************************************************************************************************************
quarkus.datasource."dbr1".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr1".jdbc.pooling-enabled=true
quarkus.datasource."dbr1".jdbc.min-size=1
quarkus.datasource."dbr1".jdbc.idle-removal-interval=50
quarkus.datasource."dbr1".jdbc.background-validation-interval=10
quarkus.datasource."dbr1".jdbc.leak-detection-interval=30
quarkus.datasource."dbr1".jdbc.acquisition-timeout=10
quarkus.datasource."dbr1".jdbc.max-lifetime=1800S

#Transactions
quarkus.datasource."dbr2".db-kind=mysql
quarkus.datasource."dbr2".username=apps_user
quarkus.datasource."dbr2".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."dbr2".jdbc.initial-size=5
quarkus.datasource."dbr2".jdbc.max-size=100
quarkus.datasource."dbr2".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr2".jdbc.url=************************************************************************************************************************************************************************************************************************
quarkus.datasource."dbr2".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr2".jdbc.pooling-enabled=true
quarkus.datasource."dbr2".jdbc.min-size=1
quarkus.datasource."dbr2".jdbc.idle-removal-interval=50
quarkus.datasource."dbr2".jdbc.background-validation-interval=10
quarkus.datasource."dbr2".jdbc.leak-detection-interval=30
quarkus.datasource."dbr2".jdbc.acquisition-timeout=10
quarkus.datasource."dbr2".jdbc.max-lifetime=1800S

#Bets
quarkus.datasource."dbr3".db-kind=mysql
quarkus.datasource."dbr3".username=apps_user
quarkus.datasource."dbr3".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."dbr3".jdbc.initial-size=5
quarkus.datasource."dbr3".jdbc.max-size=100
quarkus.datasource."dbr3".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr3".jdbc.url=****************************************************************************************************************************************************************************************************************
quarkus.datasource."dbr3".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr3".jdbc.pooling-enabled=true
quarkus.datasource."dbr3".jdbc.min-size=1
quarkus.datasource."dbr3".jdbc.idle-removal-interval=50
quarkus.datasource."dbr3".jdbc.background-validation-interval=10
quarkus.datasource."dbr3".jdbc.leak-detection-interval=30
quarkus.datasource."dbr3".jdbc.acquisition-timeout=10
quarkus.datasource."dbr3".jdbc.max-lifetime=1800S

#Bonus
quarkus.datasource."dbr4".db-kind=mysql
quarkus.datasource."dbr4".username=apps_user
quarkus.datasource."dbr4".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."dbr4".jdbc.initial-size=5
quarkus.datasource."dbr4".jdbc.max-size=100
quarkus.datasource."dbr4".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr4".jdbc.url=*****************************************************************************************************************************************************************************************************************
quarkus.datasource."dbr4".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr4".jdbc.pooling-enabled=true
quarkus.datasource."dbr4".jdbc.min-size=1
quarkus.datasource."dbr4".jdbc.idle-removal-interval=50
quarkus.datasource."dbr4".jdbc.background-validation-interval=10
quarkus.datasource."dbr4".jdbc.leak-detection-interval=30
quarkus.datasource."dbr4".jdbc.acquisition-timeout=10
quarkus.datasource."dbr4".jdbc.max-lifetime=1800S

#Write Databases
%db1.quarkus.datasource."db1".active=true
%db2.quarkus.datasource."db2".active=true
%db3.quarkus.datasource."db3".active=true
%db4.quarkus.datasource."db4".active=true

#Read Databases
%dbr1.quarkus.datasource."dbr1".active=true
%dbr2.quarkus.datasource."dbr2".active=true
%dbr3.quarkus.datasource."dbr3".active=true
%dbr4.quarkus.datasource."dbr4".active=true

quarkus.datasource.devservices.enabled=false

#logging
quarkus.log.file.level=INFO
quarkus.log.file.enable=true
quarkus.log.console.enable=false
quarkus.log.file.path=/var/log/java/mbs-app.log
quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss,SSS} : %-5p : %c:%L : %m%n
quarkus.log.file.rotation.max-file-size=5000M
quarkus.log.file.rotation.max-backup-index=10
quarkus.log.file.rotation.file-suffix=.yyyy-MM-dd
quarkus.log.file.rotation.rotate-on-boot=true

#Enable HTTP access logs
quarkus.http.access-log.enabled=true
quarkus.http.access-log.pattern=%h %l %u %t "%r" %s %b "%{i,Referer}" "%{i,User-Agent}" "%{i,X-Organization-Id}"
quarkus.log.category."io.quarkus.http.access".level=INFO
quarkus.http.access-log.category=io.quarkus.http.access
quarkus.http.access-log.log-to-file=true
quarkus.http.access-log.base-file-name=mbs-access
quarkus.http.access-log.log-directory=/var/log/java/

# Native Executable (if applicable)
#quarkus.native.enabled=true

# quarkus.container-image.registry=europe-west1-docker.pkg.dev
# quarkus.container-image.group=broker-server-341612/images
# quarkus.container-image.build=false
# quarkus.container-image.name=mbs-quarkus-app
# quarkus.jib.base-jvm-image=registry.access.redhat.com/ubi8/openjdk-21-runtime:1.20
# quarkus.container-image.push=true

# Other Optimizations
quarkus.arc.remove-unused-beans=true
quarkus.live-reload.enabled=false

#Redis
mbs.redis-port=6379
mbs.redis-user=${REDIS_USER:liden}
mbs.redis-auth=${REDIS_AUTH:eGuT7yrbJZ8d}
mbs.redis-host=${REDIS_HOST:ke-pr-redis-ha-1-node-0}
mbs.number-of-threads=200
mbs.redis-max-idle=500
mbs.redis-min-idle=100

#Rabbit
mbs.rabbit-mq-port=5672
mbs.rabbit-mq-password=${RABBIT_PASSWORD:lID3n}
mbs.rabbit-mq-username=${RABBIT_USERNAME:liden}
mbs.rabbit-mq-host=${RABBIT_HOST:rabbitmq-cluster-1-vm}

#mail
mbs.server-name=${SERVERNAME:https://mossplay.games}
mbs.launch-url=${LAUNCHURL:https://litestaging.playbetman.com"}
mbs.cipher-url=${CIPHERURL:http://ke-pr-web-1:80/mply_api/init/v1/cipher}
mbs.encryption-key=${ENVRYPTIONKEY:RJBwYSb5dFcyqUEHD300jY9nhxmi6Lnx}

#
mbs.evo-secret=${SECRET:7t6w8x1lo387f6x230fj5a1if8i13phegceuw4695aantkrl9lyxrycj10f7r3mi}
mbs.evo-currency=KES
mbs.evo-api=${API:https://apitest.fundist.org/}
mbs.evo-apikey=${APIKEY:b52341b13c3cebdb8a7f0a2317491a7a}
mbs.evo-apipass=${APIPASS:4740990224750052}
mbs.evo-stake=40
mbs.evo-void=42
mbs.evo-payout=41
mbs.evo-refunds=43

quarkus.otel.logs.exporter=none
quarkus.otel.traces.exporter=none
quarkus.otel.metrics.enabled=false
quarkus.datasource.jdbc.telemetry=false # Disable JDBC telemetry if not needed